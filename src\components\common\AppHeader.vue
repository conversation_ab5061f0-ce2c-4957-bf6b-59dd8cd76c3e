<script setup lang="ts">
import { ref, computed } from 'vue';
import UnifiedIcon from '../ui/UnifiedIcon.vue';
import { useAuthStore } from '../../stores/auth';
import { useRouter } from 'vue-router';
import { triggerSignIn, triggerSignUp } from '../../services/unifiedAuthService';

// Props
const props = defineProps<{
  leftDrawerOpen?: boolean;
}>();

// Emits
const emit = defineEmits<{
  'toggle-drawer': [];
}>();

const logoUrl = ref('/smile-factory-logo.svg');
const authStore = useAuthStore();
const router = useRouter();

// Check if user is authenticated
const isAuthenticated = computed(() => authStore.isAuthenticated);

const scrollToSignup = () => {
  const signupSection = document.getElementById('signup-section');
  if (signupSection) {
    signupSection.scrollIntoView({ behavior: 'smooth' });
  }
};

const goToDashboard = () => {
  router.push('/dashboard');
};

const toggleDrawer = () => {
  emit('toggle-drawer');
};

const goToCommunity = () => {
  router.push('/innovation-community?tab=feed');
};

const goToHome = () => {
  router.push('/');
};
</script>

<template>
  <q-header class="translucent-header q-py-sm">
    <q-toolbar>
      <div class="container q-mx-auto q-px-md">
        <div class="row full-width items-center no-wrap">
          <!-- Left Section: Hamburger Menu and Community Button -->
          <div class="col-auto flex items-center q-gutter-x-sm">
            <q-btn
              flat
              round
              dense
              icon="menu"
              class="hamburger-btn"
              @click="toggleDrawer"
              style="color: #0D8A3E"
            />
            <q-btn
              unelevated
              no-caps
              class="community-btn"
              @click="goToCommunity"
            >
              <q-icon name="people" size="16px" class="q-mr-xs" />
              <span class="gt-xs">Community</span>
              <q-badge
                color="accent"
                text-color="white"
                rounded
                floating
              >
                NEW
              </q-badge>
            </q-btn>
          </div>

          <!-- Center Section: Logo -->
          <div class="col flex items-center justify-center">
            <div class="logo-container flex items-center">
              <div class="logo-img cursor-pointer" @click="goToHome">
                <img :src="logoUrl" alt="Smile Factory Logo" class="logo-image" style="height: 50px;">
              </div>
            </div>
          </div>

          <!-- Right Section: Auth Buttons -->
          <div class="col-auto flex items-center justify-end">
            <template v-if="!isAuthenticated">
              <!-- Show button group when not authenticated -->
              <q-btn-group spread rounded class="auth-btn-group">
                <q-btn
                  color="primary"
                  no-caps
                  @click="triggerSignUp"
                  style="background-color: #0D8A3E; color: white; border: none;"
                  class="signup-btn"
                >
                  <div class="text-caption">
                    Sign Up
                  </div>
                </q-btn>
                <q-btn
                  @click="triggerSignIn"
                  no-caps
                  style="background-color: #a4ca39; color: white; border: none;"
                  class="signin-btn"
                >
                  <div class="text-caption">
                    Sign In
                  </div>
                </q-btn>
              </q-btn-group>
            </template>
            <template v-else>
              <!-- Show dashboard button when authenticated -->
              <q-btn
                color="green-9"
                label="Dashboard"
                @click="goToDashboard"
                class="q-px-sm dashboard-btn"
                outline
                round
                size="sm"
              >
                <template v-slot:prepend>
                  <unified-icon name="dashboard" class="q-mr-xs" />
                </template>
              </q-btn>
            </template>
          </div>
        </div>
      </div>
    </q-toolbar>
  </q-header>
</template>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
}

/* Solid white header with enhanced floating shadow */
.translucent-header {
  background: #ffffff !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.2);
  height: 56px;
  margin-top: -8px;
}

.translucent-header .q-toolbar {
  height: 56px;
  min-height: 56px;
  padding: 0;
  display: flex;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  height: 40px !important;
  width: auto;
}

.hamburger-btn {
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  min-width: 36px;
}

.hamburger-btn:hover {
  background-color: rgba(13, 138, 62, 0.1);
  transform: scale(1.05);
}

/* Community Button Styles */
.community-btn {
  background: rgba(13, 138, 62, 0.08);
  border: 1px solid rgba(13, 138, 62, 0.2);
  border-radius: 18px;
  color: #0D8A3E;
  font-weight: 500;
  font-size: 0.85rem;
  padding: 6px 14px;
  min-width: auto;
  height: 36px;
  transition: all 0.3s ease;
  position: relative;
  animation: pulse-glow 2s infinite;
}

.community-btn:hover {
  background: rgba(13, 138, 62, 0.12);
  border-color: rgba(13, 138, 62, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(13, 138, 62, 0.15);
  animation: none; /* Stop pulse on hover */
}

.community-btn .q-icon {
  color: #0D8A3E;
}

/* Pulse animation */
@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(13, 138, 62, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(13, 138, 62, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(13, 138, 62, 0);
  }
}

.q-btn.outline {
  border: 2px solid;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.q-btn.outline:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 138, 62, 0.2);
}

/* Auth button group styles */
.auth-btn-group {
  overflow: hidden;
  display: flex;
  border-radius: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 36px;
}

.signup-btn, .signin-btn {
  color: white;
  transition: all 0.3s ease;
  white-space: nowrap;
  padding: 6px 14px;
  border-radius: 0;
  flex: 1 1 50%;
  min-width: 0;
  letter-spacing: 0.5px;
  height: 36px;
  min-height: 36px;
  height: 36px;
  min-height: 36px;
}

.signup-btn:hover, .signin-btn:hover {
  opacity: 0.9;
}

.dashboard-btn {
  color: #0D8A3E;
  transition: all 0.3s ease;
  white-space: nowrap;
  padding: 6px 14px;
  border-radius: 18px;
  border: 1px solid #0D8A3E;
  height: 36px;
  min-height: 36px;
}

.dashboard-btn:hover {
  background-color: rgba(13, 138, 62, 0.1);
}

@media (max-width: 599px) {
  .container {
    padding: 0 8px !important;
  }

  .row {
    flex-wrap: nowrap !important;
    overflow: hidden;
  }

  .logo-image {
    height: 36px !important;
  }

  .hamburger-btn {
    width: 32px;
    height: 32px;
    min-width: 32px;
  }

  .community-btn {
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 0.75rem;
    min-width: 0;
    white-space: nowrap;
  }

  .community-btn .q-icon {
    font-size: 14px;
  }

  .community-btn .q-badge {
    font-size: 0.6rem;
    padding: 1px 4px;
  }

  /* Left section - ensure items stay together */
  .col-auto:first-child {
    flex-shrink: 0;
    min-width: 0;
  }

  /* Center section - allow logo to shrink if needed */
  .col:nth-child(2) {
    flex-shrink: 1;
    min-width: 0;
    overflow: hidden;
  }

  /* Right section - prevent shrinking */
  .col-auto:last-child {
    flex-shrink: 0;
    min-width: 0;
  }

  .q-btn.outline {
    border-radius: 20px;
    padding: 4px 12px;
    font-weight: 500;
    font-size: 0.8rem;
    white-space: nowrap;
    min-width: 80px;
  }

  .auth-btn-group {
    border-radius: 18px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    min-width: 140px;
    max-width: 140px;
  }

  .auth-btn-group .q-btn {
    flex: 1 1 50%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    box-shadow: none;
  }

  .signup-btn, .signin-btn {
    font-size: 0.75rem;
    padding: 4px 8px;
    flex: 1 1 50%;
    width: 50%;
    min-width: 0;
    max-width: 50%;
    height: auto;
    min-height: 32px;
    border-radius: 0;
    margin: 0;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .dashboard-btn {
    font-size: 0.75rem;
    padding: 4px 10px;
    min-width: 80px;
    max-width: 80px;
  }
}

@media (max-width: 400px) {
  .container {
    padding: 0 4px !important;
  }

  .community-btn {
    padding: 3px 6px;
    border-radius: 14px;
    font-size: 0.7rem;
  }

  .community-btn .q-icon {
    font-size: 12px;
  }

  .hamburger-btn {
    width: 28px;
    height: 28px;
    min-width: 28px;
  }

  .logo-image {
    height: 32px !important;
  }

  .q-btn.outline {
    padding: 4px 8px;
    font-size: 0.75rem;
    min-width: 70px;
    max-width: 70px;
  }

  .auth-btn-group {
    min-width: 120px;
    max-width: 120px;
  }

  .signup-btn, .signin-btn {
    font-size: 0.7rem;
    padding: 3px 6px;
    min-height: 28px;
  }

  .dashboard-btn {
    font-size: 0.7rem;
    padding: 3px 8px;
    min-width: 70px;
    max-width: 70px;
  }
}
</style>
